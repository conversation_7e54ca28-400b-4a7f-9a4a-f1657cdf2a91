<template>
  <div id="app">
    <PinyinGenerator />
  </div>
</template>

<script>
import PinyinGenerator from './components/PinyinGenerator.vue';

export default {
  name: 'App',
  components: {
    PinyinGenerator,
  },
};
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  margin-top: 60px;
}
</style>