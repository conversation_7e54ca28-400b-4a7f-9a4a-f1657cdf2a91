<template>
  <div id="app">
    <div v-if="currentView === 'index'" class="index-page">
      <h1>学习工具</h1>
      <div class="menu-container">
        <div class="menu-item" @click="goToPinyin">
          <div class="menu-icon">📝</div>
          <div class="menu-content">
            <h3>拼音生成</h3>
            <p>生成汉字拼音练习</p>
          </div>
        </div>
        <div class="menu-item" @click="goToMultiplication">
          <div class="menu-icon">🔢</div>
          <div class="menu-content">
            <h3>乘法竖式</h3>
            <p>乘法竖式计算练习</p>
          </div>
        </div>
      </div>
    </div>

    <div v-else-if="currentView === 'pinyin'" class="content-page">
      <button class="back-btn" @click="goToIndex">← 返回首页</button>
      <PinyinGenerator />
    </div>

    <div v-else-if="currentView === 'multiplication'" class="content-page">
      <button class="back-btn" @click="goToIndex">← 返回首页</button>
      <MultiplicationPage />
    </div>
  </div>
</template>

<script>
import PinyinGenerator from './components/PinyinGenerator.vue';
import MultiplicationPage from './MultiplicationPage.vue';

export default {
  name: 'App',
  components: {
    PinyinGenerator,
    MultiplicationPage,
  },
  data() {
    return {
      currentView: 'index', // 'index', 'pinyin', 'multiplication'
    };
  },
  methods: {
    goToIndex() {
      this.currentView = 'index';
    },
    goToPinyin() {
      this.currentView = 'pinyin';
    },
    goToMultiplication() {
      this.currentView = 'multiplication';
    },
  },
};
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  min-height: 100vh;
}

.index-page {
  text-align: center;
  padding: 60px 20px;
}

.index-page h1 {
  font-size: 2.5em;
  margin-bottom: 50px;
  color: #2c3e50;
}

.menu-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  max-width: 400px;
  margin: 0 auto;
}

.menu-item {
  background: #fff;
  border-radius: 15px;
  padding: 25px 30px;
  width: 100%;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  display: flex;
  align-items: center;
  text-align: left;
}

.menu-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #3498db;
}

.menu-icon {
  font-size: 2.5em;
  margin-right: 20px;
  flex-shrink: 0;
}

.menu-item .menu-content {
  flex: 1;
}

.menu-item h3 {
  font-size: 1.4em;
  margin: 0 0 5px 0;
  color: #2c3e50;
}

.menu-item p {
  color: #7f8c8d;
  font-size: 0.9em;
  margin: 0;
}

.content-page {
  padding: 20px;
}

.back-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1em;
  margin-bottom: 20px;
  transition: background-color 0.3s ease;
}

.back-btn:hover {
  background: #2980b9;
}

@media (max-width: 768px) {
  .menu-container {
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }

  .menu-item {
    width: 90%;
    max-width: 300px;
  }

  .index-page h1 {
    font-size: 2em;
  }
}
</style>