<template>
  <div class="pinyin-generator">
    <h1 class="title">词语拼音生成器</h1>
    <div class="settings">
      <div class="practice-columns">
        <label>练习列数：</label>
        <input type="number" v-model="practiceColumns" min="1" max="10" class="column-input">
      </div>
    </div>
    <textarea v-model="inputText" class="input-area" placeholder="请输入词语，用空格、分号或换行符分隔"></textarea>
    <div class="button-group">
      <button class="btn primary" @click="generatePinyin">生成拼音</button>
    </div>

    <!-- 弹窗 -->
    <div class="modal" v-if="pinyinList.length > 0">
      <div class="modal-content">
        <div class="modal-header">
          <h2>{{ isDictation ? '听写模式' : '拼音列表' }}</h2>
          <button class="close-btn" @click="closeModal">×</button>
        </div>

        <!-- 列表模式 -->
        <div v-if="!isDictation">
          <div class="table-container">
            <table>
              <thead>
                <tr>
                  <th>词语</th>
                  <th>拼音</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(item, index) in pinyinList" :key="index">
                  <td>{{ item.word }}</td>
                  <td>{{ item.pinyin }}</td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="modal-footer">
            <div class="modal-footer-buttons">
              <button class="btn primary" @click="startDictation">
                听写模式
              </button>
              <button class="btn secondary" @click="exportToExcel">
                导出Excel
              </button>
              <button class="btn primary" @click="printPreview">
                打印预览
              </button>
            </div>
          </div>
        </div>

        <!-- 听写模式 -->
        <div v-else class="dictation-container">
          <div class="dictation-content">
            <div class="dictation-controls">
              <div>
                <span class="word-counter"> 第 {{ currentIndex + 1 }} / {{ pinyinList.length }} 个 : </span>
                <span class="current-word" v-if="showWord"> {{ pinyinList[currentIndex].word }} </span>
                <button class="btn primary" @click="toggleShowWord">
                  {{ showWord ? '隐藏文字' : '显示文字' }}
                </button>
              </div>

              <div class="navigation-controls">
                <button class="btn secondary" @click="previousWord" :disabled="currentIndex === 0">
                  上一个
                </button>
                <button class="btn secondary" @click="nextWord" :disabled="currentIndex === pinyinList.length - 1">
                  下一个
                </button>
              </div>
              <button class="btn primary" @click="playCurrentWord">
                播放
              </button>

            </div>
          </div>
          <div class="modal-footer">
            <button class="btn secondary" @click="exitDictation">
              返回列表
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { ref, onMounted } from 'vue';
  import { pinyin } from 'pinyin-pro';
  import * as XLSX from 'xlsx';

  export default {
    name: 'PinyinGenerator',
    setup() {
      const inputText = ref('');
      const pinyinList = ref([]);
      const practiceColumns = ref(3); // 默认3列
      const isDictation = ref(false); // 听写模式
      const currentIndex = ref(0); // 听写的词
      const showWord = ref(false);
      onMounted(async () => {
        try {
          const response = await fetch('/txlist');
          if (!response.ok) throw new Error('请求失败');
          const data = await response.json();
          inputText.value = data.content.trim();
          console.log(data.content)
        } catch (error) {
          console.error('获取默认值失败:', error);
          inputText.value = ''; // 回退到备用值
        }
      });
      const generatePracticeHeaders = (count) => {
        return Array.from({ length: count }, (_, i) => `练习${i + 1}`);
      };

      const generatePinyin = () => {
        // 去除单引号和双引号
        let cleanedText = inputText.value.replace(/[,'"]+/g, '');

        // 按空格、分号或换行符切分词语
        const words = cleanedText.split(/[,\s;\n]+/).filter(word => word.trim() !== '');
        console.log(words)
        // pinyinResult.value = pinyin(inputText.value);
        // 生成拼音
        pinyinList.value = words.map(word => ({
          word: word,
          pinyin: pinyin(word)
        }));
        console.log(words.join(' '))
        // 将pinyinList.value post给 /txlist 提交给服务器 
        fetch('/txlist', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: words.join(' ')
        });
      }

      const exportToExcel = () => {
        // 准备数据
        const practiceFields = generatePracticeHeaders(practiceColumns.value);
        const data = pinyinList.value.map(item => {
          const row = {
            '词语': item.word,
            '拼音': item.pinyin,
          };
          // 添加练习列
          practiceFields.forEach(field => {
            row[field] = '';
          });
          return row;
        });

        // 创建工作簿
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(data);

        // 设置列宽
        const colWidth = 20;
        ws['!cols'] = Array(2 + practiceColumns.value).fill({ wch: colWidth });

        // 设置行高
        ws['!rows'] = Array(data.length + 1).fill({ hpt: 30 }); // +1 是为了包含表头

        // 设置单元格样式
        const range = XLSX.utils.decode_range(ws['!ref']);
        for (let R = range.s.r; R <= range.e.r; R++) {
          for (let C = range.s.c; C <= range.e.c; C++) {
            const cell_address = { c: C, r: R };
            const cell_ref = XLSX.utils.encode_cell(cell_address);

            if (!ws[cell_ref]) ws[cell_ref] = { v: '' };

            // 添加单元格样式
            ws[cell_ref].s = {
              border: {
                top: { style: 'thin', color: { auto: 1 } },
                bottom: { style: 'thin', color: { auto: 1 } },
                left: { style: 'thin', color: { auto: 1 } },
                right: { style: 'thin', color: { auto: 1 } }
              },
              alignment: {
                horizontal: 'center',
                vertical: 'center',
                wrapText: true
              },
              font: {
                name: '微软雅黑',
                sz: 12
              }
            };

            // 表头样式
            if (R === 0) {
              ws[cell_ref].s.fill = {
                patternType: 'solid',
                fgColor: { rgb: 'F8FAFC' }
              };
              ws[cell_ref].s.font.bold = true;
            }
          }
        }

        // 设置工作表属性
        ws['!protect'] = false; // 确保工作表不被保护

        // 将工作表添加到工作簿
        XLSX.utils.book_append_sheet(wb, ws, '拼音列表');

        // 导出文件
        const wopts = {
          bookType: 'xlsx',
          bookSST: false,
          type: 'binary',
          cellStyles: true
        };

        XLSX.writeFile(wb, '拼音列表.xlsx', wopts);
      };

      const closeModal = () => {
        isDictation.value = false;
        pinyinList.value = [];
      };

      const printPreview = () => {
        const practiceFields = generatePracticeHeaders(practiceColumns.value);
        const printContent = document.createElement('div');
        printContent.innerHTML = `
          <h2 style="text-align: center; margin-bottom: 20px;">拼音列表</h2>
          <table style="width: 100%; border-collapse: collapse;">
            <thead>
              <tr>
                <th style="border: 2px solid #2c3e50; padding: 12px; background-color: #f8fafc; color: #2c3e50; font-size: 16px;">词语</th>
                <th style="border: 2px solid #2c3e50; padding: 12px; background-color: #f8fafc; color: #2c3e50; font-size: 16px;">拼音</th>
                ${practiceFields.map(field => `
                  <th style="border: 2px solid #2c3e50; padding: 12px; background-color: #f8fafc; color: #2c3e50; font-size: 16px;">${field}</th>
                `).join('')}
              </tr>
            </thead>
            <tbody>
              ${pinyinList.value.map(item => `
                <tr>
                  <td style="border: 2px solid #2c3e50; padding: 12px; text-align: center; font-size: 16px;">${item.word}</td>
                  <td style="border: 2px solid #2c3e50; padding: 12px; text-align: center; font-size: 16px;">${item.pinyin}</td>
                  ${Array(practiceColumns.value).fill(`
                    <td style="border: 2px solid #2c3e50; padding: 12px; height: 50px;"></td>
                  `).join('')}
                </tr>
              `).join('')}
            </tbody>
          </table>
        `;

        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
          <html>
            <head>
              <title>拼音列表打印预览</title>
              <style>
                @media print {
                  body { 
                    margin: 0; 
                    padding: 20px; 
                    font-family: "Microsoft YaHei", "SimHei", sans-serif;
                  }
                  h2 {
                    font-size: 24px;
                    color: #2c3e50;
                    margin-bottom: 30px;
                  }
                  table { 
                    page-break-inside: auto;
                    border-collapse: collapse;
                    width: 100%;
                  }
                  tr { 
                    page-break-inside: avoid; 
                    page-break-after: auto;
                  }
                  thead { 
                    display: table-header-group; 
                  }
                  td, th {
                    border: 2px solid #2c3e50;
                  }
                  @page {
                    margin: 2cm;
                  }
                }
              </style>
            </head>
            <body>
              ${printContent.innerHTML}
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.focus();
        setTimeout(() => {
          printWindow.print();
        }, 250);
      };

      const startDictation = () => {
        isDictation.value = true;
        currentIndex.value = 0;
        showWord.value = false;
      };

      const exitDictation = () => {
        isDictation.value = false;
        currentIndex.value = 0;
        showWord.value = false;
      };

      const playCurrentWord = () => {
        const word = pinyinList.value[currentIndex.value].word;
        const utterance = new SpeechSynthesisUtterance(word);
        utterance.lang = 'zh-CN';
        speechSynthesis.speak(utterance);
      };

      const previousWord = () => {
        if (currentIndex.value > 0) {
          currentIndex.value--;
          showWord.value = false;
          // 自动播放上一个词
          setTimeout(() => {
            playCurrentWord();
          }, 100); // 添加小延迟确保状态更新
        }
      };

      const nextWord = () => {
        if (currentIndex.value < pinyinList.value.length - 1) {
          currentIndex.value++;
          showWord.value = false;
          // 自动播放下一个词
          setTimeout(() => {
            playCurrentWord();
          }, 100); // 添加小延迟确保状态更新
        }
      };

      const toggleShowWord = () => {
        showWord.value = !showWord.value;
      };

      return {
        inputText,
        pinyinList,
        practiceColumns,
        generatePinyin,
        exportToExcel,
        closeModal,
        printPreview,
        isDictation,
        currentIndex,
        showWord,
        startDictation,
        exitDictation,
        playCurrentWord,
        previousWord,
        nextWord,
        toggleShowWord,
      };
    },
  };
</script>

<style scoped>
  .pinyin-generator {
    max-width: 800px;
    margin: 2rem auto;
    padding: 2rem;
    background-color: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
  }

  .title {
    color: #2c3e50;
    font-size: 2rem;
    margin-bottom: 2rem;
    font-weight: 600;
  }

  .input-area {
    width: 100%;
    min-height: 120px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    line-height: 1.5;
    resize: vertical;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
  }

  .input-area:focus {
    outline: none;
    border-color: #4299e1;
  }

  .button-group {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 2rem;
  }

  .btn {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 500;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .btn.primary {
    background-color: #4299e1;
    color: white;
  }

  .btn.primary:hover {
    background-color: #3182ce;
  }

  .btn.secondary {
    background-color: #48bb78;
    color: white;
  }

  .btn.secondary:hover {
    background-color: #38a169;
  }

  .table-container {
    overflow-x: auto;
    margin-top: 1rem;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
  }

  th,
  td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
  }

  th {
    background-color: #f7fafc;
    font-weight: 600;
    color: #4a5568;
  }

  tr:hover {
    background-color: #f8fafc;
  }

  @media (max-width: 640px) {
    .pinyin-generator {
      padding: 1rem;
      margin: 1rem;
      width: calc(100% - 2rem);
    }

    .title {
      font-size: 1.5rem;
    }

    .button-group {
      flex-direction: column;
    }

    .btn {
      width: 100%;
    }
  }

  /* 添加弹窗相关样式 */
  .modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }

  .modal-content {
    background-color: white;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
  }

  .modal-header h2 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.5rem;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #718096;
    padding: 0.5rem;
    line-height: 1;
    transition: color 0.3s ease;
  }

  .close-btn:hover {
    color: #2d3748;
  }

  .table-container {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    max-height: 60vh;
  }

  .modal-footer {
    padding: 1rem;
    border-top: 1px solid #e2e8f0;
  }

  .modal-footer-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    /* 按钮之间的间距 */
  }

  /* 修改表格样式以适应弹窗 */
  table {
    margin: 0;
    width: 100%;
  }

  /* 响应式调整 */
  @media (max-width: 640px) {
    .modal-content {
      width: 95%;
      max-height: 95vh;
    }

    .modal-header h2 {
      font-size: 1.25rem;
    }

    .table-container {
      padding: 0.5rem;
    }

    .modal-footer-buttons {
      flex-direction: column;
      gap: 0.5rem;
    }

    .modal-footer-buttons .btn {
      width: 100%;
    }
  }

  .settings {
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
  }

  .practice-columns {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .practice-columns label {
    color: #2c3e50;
    font-size: 1rem;
  }

  .column-input {
    width: 80px;
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    font-size: 1rem;
    text-align: center;
  }

  .column-input:focus {
    outline: none;
    border-color: #4299e1;
  }

  /* 响应式调整 */
  @media (max-width: 640px) {
    .settings {
      padding: 0.5rem;
    }

    .practice-columns {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }

    .column-input {
      width: 100%;
      box-sizing: border-box;
    }
  }

  .dictation-container {
    padding: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
  }

  .dictation-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
  }

  .current-word {
    font-size: 2.5rem;
    font-weight: bold;
    color: #2c3e50;
    text-align: center;
    min-height: 4rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .dictation-controls {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    align-items: center;
    width: 100%;
  }

  .navigation-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .word-counter {
    font-size: 1rem;
    color: #64748b;
    min-width: 80px;
    text-align: center;
  }

  .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  @media (max-width: 640px) {
    .dictation-container {
      padding: 1rem;
    }

    .current-word {
      font-size: 2rem;
    }

    .navigation-controls {
      flex-direction: column;
      width: 100%;
    }

    .navigation-controls .btn {
      width: 100%;
    }
  }
</style>